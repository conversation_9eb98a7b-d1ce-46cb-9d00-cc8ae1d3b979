Helpful Resources
=================

Here, we list out a few resources that you may find helpful when climbing the steep Ivy learning curve.

**Docs for respective Backends**

`Tensorflow Documentation <https://www.tensorflow.org/api_docs>`_, `PyTorch Documentation <https://pytorch.org/docs>`_, `NumPy Documentation <https://numpy.org/doc/stable/reference/>`_ and `Jax Documentation <https://jax.readthedocs.io/>`_ are the most useful resources to find your way through the behaviours from different backends.
These are the most important resources when working on Docstrings, Ivy Frontends, and Ivy Frontends tests.

**Python - Reference**

`realpython <https://realpython.com/>`_ and `pynative <https://pynative.com/>`_ are very useful for any kind of help regarding Python.

**Stack Exchange/ Stack Overflow**

A good platform to search for any sort of information regarding python and ML.
Useful when working on almost any section in the Deep Dive.

**Co-Pilot**

GitHub Co-Pilot can be used to write any bit of code in Ivy.
They are often very useful when developing code and also help get things done faster.

**GitHub - Reference**

`Git docs <https://git-scm.com/doc>`_ is the first place you must head to when you are stuck with any issue related to git.

**IDE extension for spell checking**

Though this may sound odd, a spell-checking extension is very useful to people contributing to Ivy when adding docstrings.

**Docker**

`Docker Documentation <https://docs.docker.com/>`_ is the best place to learn more about docker.

**Github Actions**

`GitHub Actions <https://docs.github.com/en/actions>`_ can be the best place to understand Continuous Integration and how testing is done to keep our repo error free.
