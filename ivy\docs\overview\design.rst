Design
======

.. _`Deep Dive`: deep_dive.rst

This section is aimed at general users, who would like to learn how to use Ivy, and are less concerned about how it all works under the hood!

The `Deep Dive`_ section is more targeted at potential contributors, and at users who would like to dive deeper into the weeds of the framework,
and gain a better understanding of what is actually going on behind the scenes.

| (a) `Building Blocks <design/building_blocks.rst>`_
|
| (b) `Ivy's Transpiler <design/ivy_as_a_transpiler.rst>`_

.. toctree::
   :hidden:
   :maxdepth: -1
   :caption: Design

   design/building_blocks.rst
   design/ivy_as_a_transpiler.rst
