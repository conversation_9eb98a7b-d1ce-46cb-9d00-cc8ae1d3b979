Contributing
============

.. _`issues`: https://github.com/ivy-llc/ivy/issues
.. _`pull-requests`: https://github.com/ivy-llc/ivy/pulls

We welcome contributions and/or any form of engagement from absolutely anyone in the community, regardless of skill-level!

Whether you're a veteran developer 🥷 or a total beginner 🤷, everyone is welcome to create `issues`_ and create `pull-requests`_.

If you're new to any aspects of open-source development, we'll guide you through the process.

We want our ML unification journey to be as inclusive as possible, this is all only possible with a big team effort, and all are totally welcome on board for our journey! 🙂

The contributor guide is split into the sections below, it's best to go from start to finish, but you can also dive in at any stage! We're excited for you to get involved!  🦾


| (a) `Setting Up <contributing/setting_up.rst>`_
| Building the right environment 🏛️
|
| (b) `The Basics <contributing/the_basics.rst>`_
| Managing your fork 🇾, creating issues ⭕, and creating pull-requests ⬆️
|
| (c) `Building the Docs <contributing/building_the_docs.rst>`_
| How to build the documentation locally 🏗️
|
| (d) `Deep Dive <deep_dive.rst>`_
| Take a deep dive into the codebase 🤿
|
| (e) `Helpful Resources <contributing/helpful_resources.rst>`_
| Resources you would find useful when learning Ivy 📖
|
| (f) `Error Handling <contributing/error_handling.rst>`_
| Common errors you will be facing contributing to Ivy ❌

.. toctree::
   :hidden:
   :maxdepth: -1
   :caption: Contributing

   contributing/setting_up.rst
   contributing/the_basics.rst
   contributing/building_the_docs.rst
   Deep Dive <https://docs.ivy.dev/overview/deep_dive.html>
   contributing/helpful_resources.rst
   contributing/error_handling.rst

.. **Video**

.. .. raw:: html

..     <iframe width="420" height="315" allow="fullscreen;"
..     src="https://www.youtube.com/embed/HF-ZLF23g38" class="video" allowfullscreen="true">
..     </iframe>
