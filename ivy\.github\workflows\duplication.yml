name: duplication
on:
  push:
    branches:
        main
  pull_request:
permissions:
  actions: read
jobs:
  duplicate-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Checkout <PERSON>
        uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false
          fetch-depth: 100

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Check Duplicate Function
        id: tests
        run: |
          cd ivy
          python3 scripts/duplicate.py
        continue-on-error: true

      - name: Check on failures
        if: steps.tests.outcome != 'success'
        run: exit 1
