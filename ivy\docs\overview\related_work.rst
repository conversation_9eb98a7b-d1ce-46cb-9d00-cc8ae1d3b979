Related Work
============

.. _`Frameworks`: related_work/frameworks.rst
.. _`Graph Tracers`: related_work/graph_tracers.rst
.. _`Ivy vs ONNX`: related_work/ivy_vs_onnx.rst

In this section, we explain how <PERSON> compares to many other very important and related pieces of work, which also address
fragmentation in parts of the ML stack.

| (a) `Ivy vs ONNX`_
| How <PERSON> compares in functionality to ONNX
|
| (b) `Graph Tracers`_
| Extracting acyclic directed computation graphs from code
|
| (c) `Frameworks`_
| Standalone ML Frameworks

.. toctree::
   :hidden:
   :maxdepth: -1
   :caption: Related Work

   related_work/ivy_vs_onnx.rst
   related_work/graph_tracers.rst
   related_work/frameworks.rst
