# TODO: run these tests on commit/PR when we have them passing
name: frontend-tests
on:
  schedule:
    - cron: "0 0 * * 0"
  workflow_dispatch:
    inputs:
      num_examples:
        description: "Number of Hypothesis examples to run for each test"
        required: false
        default: "5"

jobs:

  torch:
    strategy:
      fail-fast: false
      matrix:
        backend: [ "jax", "numpy", "tensorflow" ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: 3.12

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Frontend Tests
        id: tests
        run: |
          cd ivy
          python3 -m pytest ivy_tests/test_ivy/test_frontends/test_torch -p no:warnings --tb=no --backend ${{ matrix.backend }} --num-examples ${{ github.event.inputs.num_examples || '5' }}
