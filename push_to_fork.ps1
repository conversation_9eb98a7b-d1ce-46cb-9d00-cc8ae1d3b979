Write-Host "========================================" -ForegroundColor Green
Write-Host "Pushing changes to fork and creating PR" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Set-Location "c:\Users\<USER>\OneDrive\Desktop\LVY\ivy"

Write-Host "Step 1: Configuring git..." -ForegroundColor Yellow
git config user.name "KALLAL"
git config user.email "<EMAIL>"

Write-Host "Step 2: Adding your fork as remote..." -ForegroundColor Yellow
git remote add fork https://github.com/7908837174/ivy-KALLAL.git

Write-Host "Step 3: Creating new branch..." -ForegroundColor Yellow
git checkout -b fix-subtract-function-issue-21937

Write-Host "Step 4: Adding modified files..." -ForegroundColor Yellow
git add ivy/functional/frontends/paddle/math.py
git add ivy_tests/test_ivy/test_frontends/test_paddle/test_math.py

Write-Host "Step 5: Checking status..." -ForegroundColor Yellow
git status

Write-Host "Step 6: Committing changes..." -ForegroundColor Yellow
git commit -m "Add subtract_ function to paddle frontend math module - Fixes #21937

- Implement missing subtract_ function in ivy/ivy/functional/frontends/paddle/math.py
- Add corresponding test case in ivy_tests/test_ivy/test_frontends/test_paddle/test_math.py
- Follows same pattern as other inplace functions like add_
- Uses ivy.inplace_update for in-place subtraction operation
- Fixes issue #21937"

Write-Host "Step 7: Pushing to your fork..." -ForegroundColor Yellow
git push -u fork fix-subtract-function-issue-21937

Write-Host "========================================" -ForegroundColor Green
Write-Host "Done! Now go to GitHub to create the PR" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Read-Host "Press Enter to continue..."
