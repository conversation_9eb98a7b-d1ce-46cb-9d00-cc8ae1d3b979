name: docstrings
on:
  push:
    branches:
      main
  pull_request:
    branches:
      main
  workflow_dispatch:
jobs:
  docstring-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Docstring Tests
        run: |
          cd ivy
          python3 -m pytest ivy_tests/test_docstrings.py -p no:warnings
