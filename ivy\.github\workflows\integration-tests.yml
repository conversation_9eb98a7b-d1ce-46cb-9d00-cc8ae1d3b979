name: integration-tests
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

permissions:
  actions: read
  contents: write

jobs:
  kornia:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        target : [ jax, numpy, tensorflow ]
    steps:
      - name: Checkout Ivy 🛎
        uses: actions/checkout@v3
        with:
          path: ivy
          persist-credentials: false
          fetch-depth: 100

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Install ivy
        run: |
          cd ivy
          pip install -e .

      - name: Run Tests
        id: tests
        run: |
          cd ivy
          scripts/shell/run_integration_tests.sh kornia ${{ matrix.target }} ${{ secrets.IVY_API_KEY }}
