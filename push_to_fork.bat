@echo off
echo ========================================
echo Pushing changes to fork and creating PR
echo ========================================

cd /d "c:\Users\<USER>\OneDrive\Desktop\LVY\ivy"

echo Step 1: Configuring git...
git config user.name "KALLAL"
git config user.email "<EMAIL>"

echo Step 2: Adding your fork as remote...
git remote add fork https://github.com/7908837174/ivy-KALLAL.git

echo Step 3: Creating new branch...
git checkout -b fix-subtract-function-issue-21937

echo Step 4: Adding modified files...
git add ivy/functional/frontends/paddle/math.py
git add ivy_tests/test_ivy/test_frontends/test_paddle/test_math.py

echo Step 5: Checking status...
git status

echo Step 6: Committing changes...
git commit -m "Add subtract_ function to paddle frontend math module - Fixes #21937

- Implement missing subtract_ function in ivy/ivy/functional/frontends/paddle/math.py
- Add corresponding test case in ivy_tests/test_ivy/test_frontends/test_paddle/test_math.py
- Follows same pattern as other inplace functions like add_
- Uses ivy.inplace_update for in-place subtraction operation
- Fixes issue #21937"

echo Step 7: Pushing to your fork...
git push -u fork fix-subtract-function-issue-21937

echo ========================================
echo Done! Now go to GitHub to create the PR
echo ========================================
pause
