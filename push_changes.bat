@echo off
cd /d "c:\Users\<USER>\OneDrive\Desktop\LVY\ivy"

echo Configuring git...
git config user.email "<EMAIL>"
git config user.name "User"

echo Creating new branch...
git checkout -b fix-subtract-function-issue-21937

echo Adding files...
git add ivy/functional/frontends/paddle/math.py
git add ivy_tests/test_ivy/test_frontends/test_paddle/test_math.py

echo Checking status...
git status

echo Committing changes...
git commit -m "Add subtract_ function to paddle frontend math module

- Implement missing subtract_ function in ivy/ivy/functional/frontends/paddle/math.py
- Add corresponding test case in ivy_tests/test_ivy/test_frontends/test_paddle/test_math.py
- Follows same pattern as other inplace functions like add_
- Uses ivy.inplace_update for in-place subtraction operation
- Fixes issue #21937"

echo Done!
pause
