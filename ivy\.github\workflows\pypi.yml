name: pypi
on:
  workflow_call:
jobs:
  upload-if-tagged-commit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️Ivy
        uses: actions/checkout@v2
        with:
          path: ivy
          persist-credentials: false

      - name: Install Dependencies
        run: |
          sudo apt-get install python3-venv
          python3 -m pip install --upgrade pip
          python3 -m pip install --upgrade build
          python3 -m pip install twine==6.0.1

      - name: Add Tag to Version
        run: |
          cd ivy
          echo "__version__ = '${{ github.ref_name }}'" > ivy/_version.py

      - name: Upload to PyPI
        if: startsWith(github.ref, 'refs/tags')
        env:
          PYPI_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          cd ivy
          bash scripts/shell/deploy_pypi.sh
