name: pr-tests
on:
  pull_request:
    branches:
      - main

permissions:
  actions: read
jobs:
  relevant-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Checkout Ivy
        uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false
          fetch-depth: 100

      - name: Fetch Commit Changes
        id: fetch_changes
        run: |
          cd ivy
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            git fetch origin main
            git diff origin/main HEAD --output="commit-diff.txt"
          else
            git diff HEAD^ HEAD --output="commit-diff.txt"
          fi

      - name: Install ivy
        run: |
          sudo pip3 install -e ivy/

      - name: Get Job URL
        uses: Tiryoh/gha-jobid-action@v0
        id: jobs
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          job_name: ${{ github.job }}

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Tests
        id: tests
        run: |
          cd ivy
          python3 scripts/run_tests_from_diff.py
