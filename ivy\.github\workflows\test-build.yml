name: python-build

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  test-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Build Test
        run: |
          cd ivy
          version=$(python -c "exec(open('ivy/_version.py').read()); print(__version__)")
          scripts/shell/test_build.sh "$version"
