name: transpiler-tests
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  transformations:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Transformations Tests
        id: tests
        run: |
          cd ivy
          scripts/shell/run_transpiler_tests.sh transformations

  translations:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Translations Tests
        id: tests
        run: |
          cd ivy
          scripts/shell/run_transpiler_tests.sh translations

  module:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Module Tests
        id: tests
        run: |
          cd ivy
          scripts/shell/run_transpiler_tests.sh module

  sourcegen:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run SourceGen Tests
        id: tests
        run: |
          cd ivy
          scripts/shell/run_transpiler_tests.sh sourcegen

  kornia:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: ivy
          persist-credentials: false

      - uses: actions/setup-python@v3
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          cd ivy
          python3 -m pip install -U -r requirements/requirements.txt
          python3 -m pip install -U -r requirements/optional.txt

      - name: Run Kornia Tests
        id: tests
        run: |
          cd ivy
          scripts/shell/run_transpiler_tests.sh kornia
