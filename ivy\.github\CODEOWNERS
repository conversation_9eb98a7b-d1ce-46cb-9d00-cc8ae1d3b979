# Ivy Framework
ivy/func_wrapper.py @<PERSON><PERSON><PERSON>
**/gradients.py @Sam-Armstrong
ivy/utils/backend @Sam-Armstrong
ivy/utils/backend/ast_helpers.py @<PERSON><PERSON><PERSON>

# Ivy Testing
ivy_tests/test_ivy/helpers/ @Sam<PERSON>Armstrong

# Docs builder
docs/index.rst @Sam-Armstrong
docs/make_docs.sh @Sam-Armstrong
docs/partial_conf.py @Sam-Armstrong
docs/prebuild.sh @Sam-Armstrong
docs/overview/contributing/building_the_docs.rst @Sam-Armstrong
docs/overview/deep_dive/building_the_docs_pipeline.rst @Sam-Armstrong
docs/_templates @Sam-Armstrong
docs/demos @<PERSON><PERSON><PERSON>

# README
README.md @<PERSON>-<PERSON>
